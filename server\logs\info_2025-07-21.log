2025-07-21 10:22:39.659 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'937' 0.001001596450805664
2025-07-21 10:22:40.711 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/openapi.json http/1.1' 200utf-8 b'90688' 0.12495923042297363
2025-07-21 11:02:36.791 | INFO     | core.task_scheduler:start:61 - 任务调度器启动成功
2025-07-21 11:02:37.923 | INFO     | core.task_scheduler:_sync_tasks_from_database:316 - 开始同步数据库中的任务到调度器
2025-07-21 11:02:37.923 | INFO     | core.task_scheduler:start_manager:89 - 任务管理器启动成功
2025-07-21 11:02:37.924 | INFO     | core.task_scheduler:shutdown:280 - 任务调度器已关闭
2025-07-21 11:02:37.924 | INFO     | core.task_scheduler:stop_manager:300 - 任务管理器已停止
2025-07-21 11:02:37.926 | INFO     | core.task_scheduler:start:61 - 任务调度器启动成功
2025-07-21 11:02:37.926 | INFO     | core.task_scheduler:_sync_tasks_from_database:316 - 开始同步数据库中的任务到调度器
2025-07-21 11:02:37.926 | INFO     | core.task_scheduler:start_manager:89 - 任务管理器启动成功
2025-07-21 11:02:37.927 | INFO     | core.task_scheduler:shutdown:280 - 任务调度器已关闭
2025-07-21 11:02:37.927 | INFO     | core.task_scheduler:stop_manager:300 - 任务管理器已停止
2025-07-21 17:19:20.732 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:19:20.733 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:19:20.734 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:19:20.734 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:07.794 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:21:07.794 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:21:07.795 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:07.796 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:21:48.170 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:21:48.170 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:21:48.171 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:21:48.171 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:24:13.955 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:24:13.956 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:24:13.957 | ERROR    | scripts.database:check_database_connection:93 - 数据库连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')
2025-07-21 17:24:13.957 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:25:36.176 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:25:36.176 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:25:36.196 | ERROR    | scripts.database:check_database_connection:94 - 数据库连接失败: object Row can't be used in 'await' expression
2025-07-21 17:25:36.199 | ERROR    | scripts.init_data:init_all_data:28 - 数据库连接失败，初始化中止
2025-07-21 17:26:52.753 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:26:52.753 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:26:52.775 | INFO     | scripts.database:check_database_connection:91 - 数据库连接正常
2025-07-21 17:26:52.776 | INFO     | scripts.init_data:init_all_data:32 - 初始化数据库表结构...
2025-07-21 17:26:52.776 | INFO     | scripts.database:init_database_tables:21 - 开始初始化数据库表结构...
2025-07-21 17:26:52.776 | INFO     | scripts.database:import_all_models:74 - 所有模型导入完成
2025-07-21 17:26:53.601 | INFO     | scripts.database:init_database_tables:34 - 数据库表结构初始化完成
2025-07-21 17:26:53.601 | INFO     | scripts.init_data:init_all_data:38 - 初始化管理员系统数据...
2025-07-21 17:26:53.601 | INFO     | scripts.admin_data:init_admin_data:29 - 开始初始化管理员系统数据...
2025-07-21 17:26:53.601 | INFO     | scripts.admin_data:init_departments:53 - 初始化部门数据...
2025-07-21 17:26:53.631 | INFO     | scripts.admin_data:init_departments:103 - 部门数据初始化完成
2025-07-21 17:26:53.631 | INFO     | scripts.admin_data:init_menus:110 - 初始化菜单数据...
2025-07-21 17:26:53.639 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (asyncmy.errors.DataError) (1406, "Data too long for column 'menu_type' at row 1")
[SQL: INSERT INTO admin_auth_menu (delete_datetime, is_delete, title, icon, path, name, component, redirect, disabled, hidden, `order`, menu_type, parent_id) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: (None, 0, '系统管理', 'system', '/system', 'System', 'Layout', None, 0, 0, 1, 'directory', None)]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-21 17:26:53.639 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:30:17.164 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:30:17.164 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:30:17.190 | INFO     | scripts.database:check_database_connection:91 - 数据库连接正常
2025-07-21 17:30:17.190 | INFO     | scripts.init_data:init_all_data:32 - 初始化数据库表结构...
2025-07-21 17:30:17.190 | INFO     | scripts.database:init_database_tables:21 - 开始初始化数据库表结构...
2025-07-21 17:30:17.190 | INFO     | scripts.database:import_all_models:74 - 所有模型导入完成
2025-07-21 17:30:17.273 | INFO     | scripts.database:init_database_tables:34 - 数据库表结构初始化完成
2025-07-21 17:30:17.273 | INFO     | scripts.init_data:init_all_data:38 - 初始化管理员系统数据...
2025-07-21 17:30:17.273 | INFO     | scripts.admin_data:init_admin_data:29 - 开始初始化管理员系统数据...
2025-07-21 17:30:17.273 | INFO     | scripts.admin_data:init_departments:53 - 初始化部门数据...
2025-07-21 17:30:17.310 | INFO     | scripts.admin_data:init_departments:103 - 部门数据初始化完成
2025-07-21 17:30:17.310 | INFO     | scripts.admin_data:init_menus:110 - 初始化菜单数据...
2025-07-21 17:30:17.323 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: 'perms' is an invalid keyword argument for AdminMenu
2025-07-21 17:30:17.324 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:34:32.485 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:34:32.487 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:34:32.504 | INFO     | scripts.database:check_database_connection:91 - 数据库连接正常
2025-07-21 17:34:32.505 | INFO     | scripts.init_data:init_all_data:32 - 初始化数据库表结构...
2025-07-21 17:34:32.505 | INFO     | scripts.database:init_database_tables:21 - 开始初始化数据库表结构...
2025-07-21 17:34:32.505 | INFO     | scripts.database:import_all_models:74 - 所有模型导入完成
2025-07-21 17:34:32.565 | INFO     | scripts.database:init_database_tables:34 - 数据库表结构初始化完成
2025-07-21 17:34:32.565 | INFO     | scripts.init_data:init_all_data:38 - 初始化管理员系统数据...
2025-07-21 17:34:32.565 | INFO     | scripts.admin_data:init_admin_data:29 - 开始初始化管理员系统数据...
2025-07-21 17:34:32.565 | INFO     | scripts.admin_data:init_departments:53 - 初始化部门数据...
2025-07-21 17:34:32.590 | INFO     | scripts.admin_data:init_departments:103 - 部门数据初始化完成
2025-07-21 17:34:32.590 | INFO     | scripts.admin_data:init_menus:110 - 初始化菜单数据...
2025-07-21 17:34:32.595 | INFO     | scripts.admin_data:init_menus:188 - 菜单数据初始化完成
2025-07-21 17:34:32.595 | INFO     | scripts.admin_data:init_roles:195 - 初始化角色数据...
2025-07-21 17:34:32.611 | ERROR    | scripts.admin_data:init_admin_data:45 - 管理员系统数据初始化失败: (sqlalchemy.exc.MissingGreenlet) greenlet_spawn has not been called; can't call await_only() here. Was IO attempted in an unexpected place?
[SQL: SELECT admin_auth_menu.id AS admin_auth_menu_id, admin_auth_menu.create_datetime AS admin_auth_menu_create_datetime, admin_auth_menu.update_datetime AS admin_auth_menu_update_datetime, admin_auth_menu.delete_datetime AS admin_auth_menu_delete_datetime, admin_auth_menu.is_delete AS admin_auth_menu_is_delete, admin_auth_menu.title AS admin_auth_menu_title, admin_auth_menu.icon AS admin_auth_menu_icon, admin_auth_menu.path AS admin_auth_menu_path, admin_auth_menu.name AS admin_auth_menu_name, admin_auth_menu.component AS admin_auth_menu_component, admin_auth_menu.redirect AS admin_auth_menu_redirect, admin_auth_menu.disabled AS admin_auth_menu_disabled, admin_auth_menu.hidden AS admin_auth_menu_hidden, admin_auth_menu.`order` AS admin_auth_menu_order, admin_auth_menu.menu_type AS admin_auth_menu_menu_type, admin_auth_menu.parent_id AS admin_auth_menu_parent_id 
FROM admin_auth_menu, admin_auth_role_menus 
WHERE %s = admin_auth_role_menus.role_id AND admin_auth_menu.id = admin_auth_role_menus.menu_id]
[parameters: [{'%(2459658676240 param)s': 1}]]
(Background on this error at: https://sqlalche.me/e/20/xd2s)
2025-07-21 17:34:32.611 | ERROR    | scripts.init_data:init_all_data:40 - 管理员系统数据初始化失败，初始化中止
2025-07-21 17:40:05.855 | INFO     | scripts.init_data:init_all_data:23 - 开始初始化 dev 环境数据...
2025-07-21 17:40:05.855 | INFO     | scripts.init_data:init_all_data:26 - 检查数据库连接...
2025-07-21 17:40:05.875 | INFO     | scripts.database:check_database_connection:91 - 数据库连接正常
2025-07-21 17:40:05.875 | INFO     | scripts.init_data:init_all_data:32 - 初始化数据库表结构...
2025-07-21 17:40:05.879 | INFO     | scripts.database:init_database_tables:21 - 开始初始化数据库表结构...
2025-07-21 17:40:05.879 | INFO     | scripts.database:import_all_models:74 - 所有模型导入完成
2025-07-21 17:40:05.921 | INFO     | scripts.database:init_database_tables:34 - 数据库表结构初始化完成
2025-07-21 17:40:05.921 | INFO     | scripts.init_data:init_all_data:38 - 初始化管理员系统数据...
2025-07-21 17:40:05.921 | INFO     | scripts.admin_data:init_admin_data:29 - 开始初始化管理员系统数据...
2025-07-21 17:40:05.921 | INFO     | scripts.admin_data:init_departments:53 - 初始化部门数据...
2025-07-21 17:40:05.937 | INFO     | scripts.admin_data:init_departments:103 - 部门数据初始化完成
2025-07-21 17:40:05.937 | INFO     | scripts.admin_data:init_menus:110 - 初始化菜单数据...
2025-07-21 17:40:05.956 | INFO     | scripts.admin_data:init_menus:188 - 菜单数据初始化完成
2025-07-21 17:40:05.956 | INFO     | scripts.admin_data:init_roles:195 - 初始化角色数据...
2025-07-21 17:40:05.962 | INFO     | scripts.admin_data:init_roles:236 - 角色数据初始化完成
2025-07-21 17:40:05.962 | INFO     | scripts.admin_data:init_users:243 - 初始化用户数据...
2025-07-21 17:40:06.211 | INFO     | scripts.admin_data:init_users:282 - 用户数据初始化完成
2025-07-21 17:40:06.212 | INFO     | scripts.admin_data:init_admin_data:41 - 管理员系统数据初始化完成
2025-07-21 17:40:06.212 | INFO     | scripts.init_data:init_all_data:44 - 初始化系统配置数据...
2025-07-21 17:40:06.212 | INFO     | scripts.system_data:init_system_data:34 - 开始初始化系统配置数据...
2025-07-21 17:40:06.212 | INFO     | scripts.system_data:init_dict_data:61 - 初始化字典数据...
2025-07-21 17:40:06.235 | INFO     | scripts.system_data:init_dict_data:128 - 字典数据初始化完成
2025-07-21 17:40:06.235 | INFO     | scripts.system_data:init_settings_data:138 - 初始化系统设置数据...
2025-07-21 17:40:06.254 | INFO     | scripts.system_data:init_settings_data:219 - 系统设置数据初始化完成
2025-07-21 17:40:06.254 | INFO     | scripts.system_data:init_system_data:46 - 系统配置数据初始化完成
2025-07-21 17:40:06.254 | INFO     | scripts.init_data:init_all_data:48 - dev 环境数据初始化完成！
2025-07-21 17:40:06.254 | INFO     | scripts.init_data:init_all_data:49 - 默认管理员账号: 13800138000
2025-07-21 17:40:06.254 | INFO     | scripts.init_data:init_all_data:50 - 默认管理员密码: admin123
2025-07-21 17:42:02.943 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'937' 0.0013773441314697266
2025-07-21 17:42:03.292 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/openapi.json http/1.1' 200utf-8 b'90985' 0.11404943466186523
2025-07-21 17:46:52.807 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D577E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D577E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D10C80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D57740>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\favicon.ico'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:46:52.851 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/favicon.ico http/1.1' 404utf-8 b'34' 0.0530850887298584
2025-07-21 17:51:31.081 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C280>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57C40>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57C40>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D4BE00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D64FE0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:51:31.092 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.017829179763793945
2025-07-21 17:52:24.937 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4CF40>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65080>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4C290>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65620>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64EA0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:52:24.945 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.*****************
2025-07-21 17:52:46.130 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DBD0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65120>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4D040>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D659E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D653A0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:52:46.140 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.010003089904785156
2025-07-21 17:53:09.625 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4E9B0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D656C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D656C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4E570>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D66020>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65A80>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:53:09.632 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.008280038833618164
2025-07-21 17:54:17.273 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4DFC0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57E20>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D57E20>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8D12240>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D57B00>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D579C0>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:54:17.292 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.****************
2025-07-21 17:55:20.796 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4C7C0>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D659E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D659E0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4D8B0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D65260>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65940>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 17:55:20.810 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/vadmin/system/settings/base/config http/1.1' 404utf-8 b'34' 0.019055843353271484
2025-07-21 17:55:57.174 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/admin/system/settings/base/config http/1.1' 200utf-8 b'142' 0.001748800277709961
2025-07-21 18:05:41.310 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D660C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D660C0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4F740>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D654E0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D674C0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:05:41.319 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.0776510238647461
2025-07-21 18:09:11.124 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D662A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D662A0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64A40>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67560>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D64FE0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:09:11.135 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.010373353958129883
2025-07-21 18:10:01.020 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67600>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D67600>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64110>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67B00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D676A0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:10:01.024 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.005261898040771484
2025-07-21 18:10:17.542 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle <_asyncio.TaskStepMethWrapper object at 0x0000026EE8E4D750>()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1DA0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8CE1DA0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4DCD0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 765, in app
    await self.default(scope, receive, send)
          │    │       │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D663E0>
          │    │       │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8CE1940>
          │    │       └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.not_found of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 651, in not_found
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:10:17.552 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'POST http://127.0.0.1:9000/auth/login http/1.1' 404utf-8 b'34' 0.009313344955444336
2025-07-21 18:11:33.327 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65BC0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65BC0>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E4DC10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D662A0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D66160>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:11:33.335 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.008668184280395508
2025-07-21 18:19:43.087 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65440>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65440>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64A10>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D67420>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67560>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:19:43.096 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.011059045791625977
2025-07-21 18:20:24.088 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://localhost:9000/docs http/1.1' 200utf-8 b'937' 0.0
2025-07-21 18:20:52.109 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65940>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E64C80>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8D651C0>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D67F60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:20:52.115 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.005370140075683594
2025-07-21 18:22:48.378 | ERROR    | core.exception:unicorn_exception_handler:66 - 404: Not Found
Traceback (most recent call last):

  File "F:\share\mxtt\server\main.py", line 179, in <module>
    shell_app()
    └ <typer.main.Typer object at 0x0000026EE0D3EF90>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 323, in __call__
    return get_command(self)(*args, **kwargs)
           │           │      │       └ {}
           │           │      └ ()
           │           └ <typer.main.Typer object at 0x0000026EE0D3EF90>
           └ <function get_command at 0x0000026EE581EFC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1161, in __call__
    return self.main(*args, **kwargs)
           │    │     │       └ {}
           │    │     └ ()
           │    └ <function TyperGroup.main at 0x0000026EE5817EC0>
           └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 743, in main
    return _main(
           └ <function _main at 0x0000026EE5816FC0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\core.py", line 198, in _main
    rv = self.invoke(ctx)
         │    │      └ <click.core.Context object at 0x0000026EE738CCE0>
         │    └ <function MultiCommand.invoke at 0x0000026EE429A660>
         └ <TyperGroup >
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1697, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
           │               │       │       │      └ <click.core.Context object at 0x0000026EE7551070>
           │               │       │       └ <function Command.invoke at 0x0000026EE429A020>
           │               │       └ <TyperCommand run>
           │               └ <click.core.Context object at 0x0000026EE7551070>
           └ <function MultiCommand.invoke.<locals>._process_result at 0x0000026EE7B0DE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 1443, in invoke
    return ctx.invoke(self.callback, **ctx.params)
           │   │      │    │           │   └ {'host': '0.0.0.0', 'port': 9000}
           │   │      │    │           └ <click.core.Context object at 0x0000026EE7551070>
           │   │      │    └ <function run at 0x0000026EE7B0DA80>
           │   │      └ <TyperCommand run>
           │   └ <function Context.invoke at 0x0000026EE42989A0>
           └ <click.core.Context object at 0x0000026EE7551070>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\click\core.py", line 788, in invoke
    return __callback(*args, **kwargs)
                       │       └ {'host': '0.0.0.0', 'port': 9000}
                       └ ()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\typer\main.py", line 698, in wrapper
    return callback(**use_params)
           │          └ {'host': '0.0.0.0', 'port': 9000}
           └ <function run at 0x0000026EE736DE40>

  File "F:\share\mxtt\server\main.py", line 85, in run
    uvicorn.run(app='main:create_app', host=host, port=port, lifespan="on", factory=True)
    │       │                               │          └ 9000
    │       │                               └ '0.0.0.0'
    │       └ <function run at 0x0000026EE42CFE20>
    └ <module 'uvicorn' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\uvicorn\\__init__....

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\main.py", line 587, in run
    server.run()
    │      └ <function Server.run at 0x0000026EE4300180>
    └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\uvicorn\server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ None
           │       │   │    └ <function Server.serve at 0x0000026EE4300220>
           │       │   └ <uvicorn.server.Server object at 0x0000026EE8B145F0>
           │       └ <function run at 0x0000026EE2B0E980>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x0000026EE4288B40>
           │      └ <function Runner.run at 0x0000026EE2F822A0>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-p...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x0000026EE2F7BE20>
           │    └ <ProactorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x0000026EE8B14590>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 678, in run_until_complete
    self.run_forever()
    │    └ <function ProactorEventLoop.run_forever at 0x0000026EE3037B00>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 645, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x0000026EE2F81BC0>
    └ <ProactorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1999, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x0000026EE2AF5EE0>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup(<Future finis...lt=('', None)>)>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\base.py", line 141, in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
          │    │   │      │                      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65D00>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <starlette.middleware.base.BaseHTTPMiddleware object at 0x0000026EE8CF1D90>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.send_no_error at 0x0000026EE8D65D00>
          │                            │    │    │     │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x0000026EE8E66900>
          │                            │    └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x0000026EE8CF1F70>
          └ <function wrap_app_handling_exceptions at 0x0000026EE40A1080>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │                │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x0000026EE8B93620>>
          └ <fastapi.routing.APIRouter object at 0x0000026EE8B93620>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │     │      │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │     └ <function Mount.handle at 0x0000026EE40A2E80>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\routing.py", line 460, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x0000026EE8E74360>
          │    │   │      └ <function BaseHTTPMiddleware.__call__.<locals>.call_next.<locals>.receive_or_disconnect at 0x0000026EE8D65E40>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
          │    └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
          └ Mount(path='/media', name='', app=<starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 98, in __call__
    response = await self.get_response(path, scope)
                     │    │            │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9000), 'cl...
                     │    │            └ 'system\\logo.png'
                     │    └ <function StaticFiles.get_response at 0x0000026EE436CF40>
                     └ <starlette.staticfiles.StaticFiles object at 0x0000026EE8B935F0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\starlette\staticfiles.py", line 149, in get_response
    raise HTTPException(status_code=404)
          └ <class 'starlette.exceptions.HTTPException'>

starlette.exceptions.HTTPException: 404: Not Found
2025-07-21 18:22:48.386 | INFO     | core.middleware:write_request_log:33 - basehttp.log_message: 'GET http://127.0.0.1:9000/media/system/logo.png http/1.1' 404utf-8 b'34' 0.009583234786987305
